<?php
/**
 * Test script to verify the latest array structure refactoring
 */

// Define the root path (normally done by ImpressCMS)
define('ICMS_ROOT_PATH', __DIR__ . '/htdocs');

// Include the necessary files
require_once ICMS_ROOT_PATH . '/libraries/icms/core/VersioncheckerInterface.php';
require_once ICMS_ROOT_PATH . '/libraries/icms/core/Versionchecker.php';
require_once ICMS_ROOT_PATH . '/libraries/icms/core/Versionchecker_RSS.php';

// Mock the constants that would normally be defined by ImpressCMS
if (!defined('ICMS_VERSION_NAME')) {
    define('ICMS_VERSION_NAME', 'ImpressCMS 2.0.1');
}
if (!defined('ICMS_VERSION_BUILD')) {
    define('ICMS_VERSION_BUILD', 113);
}
if (!defined('_AM_VERSION_CHECK_RSSDATA_EMPTY')) {
    define('_AM_VERSION_CHECK_RSSDATA_EMPTY', 'No RSS data available');
}

echo "Testing Latest Array Structure Refactoring...\n\n";

// Test 1: Check if base class has the latest array
echo "1. Testing base class latest array structure: ";
$reflection = new ReflectionClass('icms_core_Versionchecker');
$properties = $reflection->getProperties();
$hasLatestArray = false;
foreach ($properties as $property) {
    if ($property->getName() === 'latest') {
        $hasLatestArray = true;
        break;
    }
}
if ($hasLatestArray) {
    echo "✓ PASS\n";
} else {
    echo "✗ FAIL\n";
}

// Test 2: Check if RSS implementation can be instantiated
echo "2. Testing RSS implementation instantiation: ";
try {
    $rssChecker = icms_core_Versionchecker_RSS::getInstance();
    if ($rssChecker instanceof icms_core_Versionchecker_RSS) {
        echo "✓ PASS\n";
    } else {
        echo "✗ FAIL\n";
    }
} catch (Exception $e) {
    echo "✗ FAIL: " . $e->getMessage() . "\n";
}

// Test 3: Check if latest array has correct structure
echo "3. Testing latest array structure: ";
try {
    $rssChecker = icms_core_Versionchecker_RSS::getInstance();
    $latest = $rssChecker->getLatest();
    $expectedKeys = ['version_name', 'build', 'status', 'url', 'changelog'];
    $hasAllKeys = true;
    foreach ($expectedKeys as $key) {
        if (!array_key_exists($key, $latest)) {
            $hasAllKeys = false;
            break;
        }
    }
    if ($hasAllKeys) {
        echo "✓ PASS\n";
    } else {
        echo "✗ FAIL (missing keys)\n";
    }
} catch (Exception $e) {
    echo "✗ FAIL: " . $e->getMessage() . "\n";
}

// Test 4: Check if getter methods work with new structure
echo "4. Testing getter methods: ";
try {
    $rssChecker = icms_core_Versionchecker_RSS::getInstance();
    $methods = ['getLatestVersionName', 'getLatestBuild', 'getLatestStatus', 'getLatestUrl', 'getLatestChangelog', 'getLatest'];
    $allMethodsExist = true;
    foreach ($methods as $method) {
        if (!method_exists($rssChecker, $method)) {
            $allMethodsExist = false;
            break;
        }
    }
    if ($allMethodsExist) {
        echo "✓ PASS\n";
    } else {
        echo "✗ FAIL (missing methods)\n";
    }
} catch (Exception $e) {
    echo "✗ FAIL: " . $e->getMessage() . "\n";
}

echo "\nLatest array structure test completed!\n";
