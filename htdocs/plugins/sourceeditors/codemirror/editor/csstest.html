<html xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <script src="js/codemirror.js" type="text/javascript"></script>
    <title>CodeMirror: CSS demonstration</title>
    <link rel="stylesheet" type="text/css" href="css/docs.css"/>
  </head>
  <body style="padding: 20px;">

<p>Demonstration of <a href="index.html">CodeMirror</a>'s CSS
highlighter.</p>

<div class="border">
<textarea id="code" cols="120" rows="30">
/* Some example CSS */

@import url("something.css");

body {
  margin: 0;
  padding: 3em 6em;
  font-family: tahoma, arial, sans-serif;
  color: #000;
}

#navigation a {
  font-weight: bold;
  text-decoration: none !important;
}

h1 {
  font-size: 2.5em;
}

h2 {
  font-size: 1.7em;
}

h1:before, h2:before {
  content: "::";
}

code {
  font-family: courier, monospace;
  font-size: 80%;
  color: #418A8A;
}
</textarea>
</div>

<script type="text/javascript">
  var editor = CodeMirror.fromTextArea('code', {
    height: "350px",
    parserfile: "parsecss.js",
    stylesheet: "css/csscolors.css",
    path: "js/"
  });
</script>

  </body>
</html>
