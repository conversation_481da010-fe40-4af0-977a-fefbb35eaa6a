<html xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <title>CodeMirror: In-browser code editing</title>

    <link rel="stylesheet" type="text/css" href="css/docs.css"/>
    <style type="text/css">
      div.top {text-align: center;}
      div.top h1 {margin-bottom: 0;}
      div.top h2 {margin-top: 0; margin-bottom: 1.5em;}
      div.donate span {cursor: pointer; text-decoration: underline;}
      div.donate {font-size: 70%; margin-top: 1em; width: 155px; padding: 10px; border: 1px solid #c44;}
    </style>
  </head>
  <body>

<div class="top">
  <h1>CodeMirror</h1>
  <h2 class="underline">In-browser code editing made almost bearable</h2>
</div>
<div style="float: right; padding-left: 10px;">
  <form action="https://www.paypal.com/cgi-bin/webscr" method="post" id="paypal">
    <input type="hidden" name="cmd" value="_s-xclick"/>
    <input type="hidden" name="hosted_button_id" value="3701544"/>
  </form>
  <img src="css/people.jpg" alt=""/><br/>
  <div class="donate">
    Make a donation:
    <ul style="margin: 0 2em; padding: 0">
      <li><span onclick="document.getElementById('paypal').submit();">Paypal</span></li>
      <li><span onclick="document.getElementById('bankinfo').style.display = 'block';">Bank</span></li>
    </ul>
    <div id="bankinfo" style="display: none; font-size: 80%;">
      Bank: <i>Rabobank</i><br/>
      Country: <i>Netherlands</i><br/>
      SWIFT: <i>RABONL2U</i><br/>
      Account: <i>*********</i><br/>
      Name: <i>Marijn Haverbeke</i><br/>
      IBAN: <i>NL26 RABO 0147 8507 70</i>
    </div>
  </div>
</div>

<p>CodeMirror is a JavaScript library that can be used to create a
relatively pleasant editor interface for code-like content &#x2015;
computer programs, HTML markup, and similar. If a parser has been
written for the language you are editing (see below for a list of
supported languages), the code will be coloured, and the editor will
help you with indentation.</p>

<p>To get a look at CodeMirror, see the test pages for the various
parsers...</p>

<ul>
  <li><a href="jstest.html">JavaScript</a></li>
  <li><a href="htmltest.html">XML/HTML</a></li>
  <li><a href="csstest.html">CSS</a></li>
  <li><a href="sparqltest.html">SPARQL</a></li>
  <li><a href="mixedtest.html">HTML mixed-mode</a></li>
  <li><a href="contrib/php/index.html">HTML+PHP mixed-mode</a> (courtesy of <a href="contrib/php/LICENSE">Yahoo!</a>)</li>
  <li><a href="contrib/python/index.html">Python</a> (courtesy of <a href="contrib/python/LICENSE">Timothy Farrell</a>)</li>
  <li><a href="contrib/lua/index.html">Lua</a> (courtesy of <a href="http://francio.pl/">Franciszek Wawrzak</a>)</li>
  <li><a href="http://stuff.hantl.cz/ruby-in-codemirror/">Ruby</a> (by Michal Hantl, <a href="http://github.com/hakunin/ruby-in-codemirror/tree/master">unfinished</a>)</li>
</ul>

<p>Or take a look at some real-world uses of the system...</p>

<ul>
  <li><a href="http://github.com/darwin/firerainbow">FireRainbow: syntax colouring for Firebug</a></li>
  <li><a href="http://dev.freebaseapps.com/">Freebase's Acre IDE</a></li>
  <li><a href="http://kml-samples.googlecode.com/svn/trunk/interactive/index.html">Google Earth KML sampler</a></li>
  <li><a href="http://eloquentjavascript.net/chapter1.html">Eloquent JavaScript's console</a></li>
  <li><a href="http://demo.qooxdoo.org/current/playground/#Hello_World">The qooxdoo playground</a></li>
  <li><a href="http://billmill.org/static/canvastutorial/index.html">A cool tutorial about the &lt;canvas> element</a></li>
  <li><a href="http://orc.csres.utexas.edu/tryorc.shtml">An online IDE for the Orc programming language</a></li>
  <li><a href="http://code.google.com/apis/ajax/playground">Google's API playground</a></li>
</ul>

<h2>Releases</h2>

<p class="rel"><em>30-05-2009</em>: <a
href="http://marijn.haverbeke.nl/codemirror/codemirror-0.62.zip">Version
0.62</a>: Introduces <a href="contrib/python/index.html">Python</a>
and <a href="contrib/lua/index.html">Lua</a> parsers. Add
<code>setParser</code> (on-the-fly mode changing) and
<code>clearHistory</code> methods. Make parsing passes time-based
instead of lines-based (see the <code>passTime</code> option).</p>

<p class="rel"><em>04-03-2009</em>: <a
href="http://marijn.haverbeke.nl/codemirror/codemirror-0.61.zip">Version
0.61</a>: Add line numbers support (see <code>lineNumbers</code>
option in <a href="manual.html">manual</a>). Support a mode where tab
'shifts' indentation instead of resetting it (see
<code>tabMode="shift"</code>). Add <code>indentUnit</code> option to
configure indentation depths. Make it possible to grab the editor's
keyboard input, which is useful when popping up dialogs (see
<code>grabKeys</code>/<code>ungrabKeys</code>). Fix a lot of small
bugs, among which the various issues related to pasting in Internet
Explorer.</p>

<p class="rel"><em>29-12-2008</em>: <a
href="http://marijn.haverbeke.nl/codemirror/codemirror-0.60.zip">Version
0.60</a>: This release makes lots of internal changes, so test before
you upgrade. More robust selection-preservation on IE, allowing styles
with different font sizes. New <code>activeTokens</code> and
<code>cursorActivity</code> callbacks, and a more powerful, line-based
interface for inspecting and manipulating the content of the editor
(see <a href="manual.html">manual</a>). Fixes the
<code>replaceSelection</code> problem in IE, and a lot of other,
smaller issues.</p>

<p class="rel"><em>28-09-2008</em>: <a
href="http://marijn.haverbeke.nl/codemirror/codemirror-0.58.zip">Version
0.58</a>: Add parsers for SPARQL and HTML-mixed-mode (nests CSS and JS
parsers). Also: bracket highlighting, a 'dumb tabs' mode, an
<code>onChange</code> callback, and heaps of bugfixes. See the manual
for details on the new features.</p>

<p class="rel"><em>04-07-2008</em>: <a
href="http://marijn.haverbeke.nl/codemirror/codemirror-0.57.zip">Version
0.57</a>: A CSS parser and a nice tokenizer framework, bugfixes in the
XML parser, a few browser-issue workarounds, one of which should fix
the age-old Firefox cursor-showing-on-wrong line bug.</p>

<h2 id="supported">Supported browsers</h2>

<p>At this time, the following browsers are supported:</p>

<ul>
  <li>Firefox 1.5 or higher</li>
  <li>Internet Explorer 6 or higher</li>
  <li>Safari 3 or higher</li>
  <li>Opera 9.52 or higher</li>
  <li>Chrome</li>
</ul>

<p>Making it work on other browsers that have decent support for the
W3C DOM model should not be too hard, but I am not actively testing
against those.</p>

<h2>Getting the code</h2>

<p>All of CodeMirror is released under a <a
href="LICENSE">zlib-style</a> license. To get it, you can download the
<a href="http://marijn.haverbeke.nl/codemirror/codemirror.zip">latest
release</a> or the current <a
href="http://marijn.haverbeke.nl/codemirror/codemirror-latest.zip">development
snapshot</a> as zip files, or use the <a
href="http://www.darcs.net/">darcs</a> version control system to get
the repository:</p>

<pre class="code">darcs get http://marijn.haverbeke.nl/codemirror</pre>

<p>This second method is recommended if you are planning to hack on
CodeMirror &#x2015; it makes it easy to record your patches and share them
with me. To see the repository online, visit the <a
href="http://marijn.haverbeke.nl/darcsweb.cgi?r=CodeMirror">CodeMirror
darcsweb</a>.</p>

<h2>Support</h2>

<p>There is a <a
href="http://groups.google.com/group/codemirror">Google group</a> (a
sort of mailing list/newsgroup thingy) for discussion and news related
to CodeMirror. You can also e-mail me directly: <a
href="mailto:<EMAIL>">Marijn Haverbeke</a>.</p>

<h2>Documentation</h2>

<ul>
  <li>The <a href="manual.html">manual</a> is all most users will need
  to read (or skim).</li>
  <li>If you're interested in working on the code, <a
  href="story.html">this document</a> about CodeMirror's architecture
  will be useful.</li>
  <li>The <a
  href="http://marijn.haverbeke.nl/darcsweb.cgi?r=CodeMirror;a=tree">source
  code</a> is, for the most part, rather well commented, so if all
  else fails, you can try reading it.</li>
</ul>

  </body>
</html>
