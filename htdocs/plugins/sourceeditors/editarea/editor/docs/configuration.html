<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en" >
<head>
	<title>EditArea documentation</title>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<link href="doc_style.css" rel="stylesheet" type="text/css" />
</head>
<body>
	<div class='header'>
		<h1>Configuration reference</h1>
	</div>
	<div class='content'>
		<p>This document is the index/reference page for all available core configuration 
			options in EditArea.</p>
		<div class="separator"></div>
		
		<h2>Configuration options</h2>
		<p>All configuration options below is to be placed within the init JavaScript call.<br />
		<ul class='optionlist'>
			<li><strong>Needed option</strong>
				<ul>
					<li><strong>id</strong>: should contain the id of the textarea that should be converted into an editor
						<br /><span class='underline'>Type</span>: String
						<br /><span class='underline'>Default</span>: null</li>
				</ul>
				<br />
			</li>
			<li><strong>General</strong>
				<ul>
				<li><strong>language</strong>: should contain a code of the language pack to be used for translation. If EditArea doesn't have a language pack for your language you could always write your own and contribute this back to this project by uploading it as a Patch at <a href='http://sourceforge.net/projects/editarea/'>SourceForge</a>.
						<br /><span class='underline'>Type</span>: String
						<br /><span class='underline'>Default</span>: "en"
					</li>
					<li><strong>syntax</strong>: should contain a code of the syntax definition file that must be used for the highlight mode.
						<br /><span class='underline'>Type</span>: String
						<br /><span class='underline'>Default</span>: ""
					</li>
					<li><strong>start_highlight</strong>: set if the editor should start with highlighted syntax displayed.
						<br /><span class='underline'>Type</span>: Boolean
						<br /><span class='underline'>Default</span>: false
					</li>
					<li><strong>is_multi_files</strong>: determine if the editor load the content of the textarea (false) or if it wait for an openFile() call for allowing file editing.
						<br /><span class='underline'>Type</span>: Boolean
						<br /><span class='underline'>Default</span>: false
					</li>
					<li><strong>min_width</strong>: define the minimum width of the editor
						<br /><span class='underline'>Type</span>: Integer
						<br /><span class='underline'>Default</span>: 400
					</li>			
					<li><strong>min_height</strong>: define the minimum height of the editor
						<br /><span class='underline'>Type</span>: Integer
						<br /><span class='underline'>Default</span>: 100
					</li>			
					<li><strong>allow_resize</strong>: define one with axis the editor can be resized by the user.
						<br /><span class='underline'>Type</span>: String ("no" (no resize allowed), "both" (x and y axis), "x", "y")
						<br /><span class='underline'>Default</span>: "both"
					</li>
					<li><strong>allow_toggle</strong>: define if a toggle button must be added under the editor in order to allow to toggle between the editor and the orginal textarea.
						<br /><span class='underline'>Type</span>: Boolean
						<br /><span class='underline'>Default</span>: true
					</li>
					<li><strong>plugins</strong>: a comma separated list of plugins to load.
						<br /><span class='underline'>Type</span>: String 
						<br /><span class='underline'>Default</span>: ""
					</li>
					<li><strong>browsers</strong>: define if the editor must be loaded only when the user navigotr is known to be a working one, or if it will be loaded for all navigators.
						<br /><span class='underline'>Type</span>: String ("all" or "known")
						<br /><span class='underline'>Default</span>: "known"
					</li>
					<li><strong>display</strong>: specify when the textarea will be converted into an editor. If set to &quot;later&quot;, the toogle button will be displayed to allow later conversion. 
						<br /><span class='underline'>Type</span>: String ("onload" or "later")
						<br /><span class='underline'>Default</span>: "onload"
					</li>
					<li><strong>toolbar</strong>: define the toolbar that will be displayed, each element being separated by a ",".
						<br /><span class='underline'>Type</span>: String (combinaison of: "|", "*", "search", "go_to_line", "undo", "redo", "change_smooth_selection", "reset_highlight", "highlight", "word_wrap", "help", "save", "load", "new_document", "syntax_selection")
						<br />"|" or "separator" make appears a separator in the toolbar.
						<br />"*" or "return" make appears a line-break in the toolbar
						<br /><span class='underline'>Default</span>: "search, go_to_line, fullscreen, |, undo, redo, |, select_font,|, change_smooth_selection, highlight, reset_highlight, word_wrap, |, help"
					</li>
					<li><strong>begin_toolbar</strong>: toolbar button list to add before the toolbar defined by the "toolbar" option.
						<br /><span class='underline'>Type</span>: String (cf. "toolbar" option)
						<br /><span class='underline'>Default</span>: ""
					</li>
					<li><strong>end_toolbar</strong>: toolbar button list to add after the toolbar defined by the "toolbar" option.
						<br /><span class='underline'>Type</span>: String (cf. "toolbar" option)
						<br /><span class='underline'>Default</span>: ""
					</li>
					<li><strong>font_size</strong>: define the font-size used to display the text in the editor.
						<br /><span class='underline'>Type</span>: Integer
						<br /><span class='underline'>Default</span>: 10
					</li>
					<li><strong>font_family</strong>: define the font-familly used to display the text in the editor. (eg: "monospace" or "verdana,monospace"). Opera will always use "monospace".
						<br /><span class='underline'>Type</span>: String
						<br /><span class='underline'>Default</span>: "monospace"
					</li>
					<li><strong>cursor_position</strong>: define if the cursor should be placed where it was in the textarea before replacement (auto) or at the beginning of the file (begin).
						<br /><span class='underline'>Type</span>: String ("begin" or "auto")
						<br /><span class='underline'>Default</span>: "begin"
					</li>
					<li><strong>gecko_spellcheck</strong>: allow to disable/enable the Firefox 2 spellchecker
						<br /><span class='underline'>Type</span>: Boolean
						<br /><span class='underline'>Default</span>: false
					</li>
					<li><strong>max_undo</strong>: number of undo action allowed
						<br /><span class='underline'>Type</span>: Integer
						<br /><span class='underline'>Default</span>: 20
					</li>
					<li><strong>fullscreen</strong>: determine if EditArea start in fullscreen mode or not
						<br /><span class='underline'>Type</span>: Boolean
						<br /><span class='underline'>Default</span>: false
					</li>
					<li><strong>is_editable</strong>: determine if EditArea display only the highlighted syntax (no edition possiblities, no toolbars). 
							It's possible to switch the editable mode whenever you want (code example for a toggle edit mode: 	<em>editAreaLoader.execCommand('editor_id', 'set_editable', !editAreaLoader.execCommand('editor_id', 'is_editable'));</em>).
						<br /><span class='underline'>Type</span>: Boolean
						<br /><span class='underline'>Default</span>: true
					</li>
					<li><strong>word_wrap</strong>: determine if the text will be automatically wrapped to the next line when it reach the end of a line. This is linked ot the word_wrap icon available in the toolbar.
							
						<br /><span class='underline'>Type</span>: Boolean
						<br /><span class='underline'>Default</span>: false
					</li>
					<li><strong>replace_tab_by_spaces</strong>: define the number of spaces that will replace tabulations (\t) in text. If tabulation should stay tabulation, set this option to false.
						<br /><span class='underline'>Type</span>: Integer (or false)
						<br /><span class='underline'>Default</span>: false
					</li>
					<li><strong>debug</strong>: used to display some debug information into a newly created textarea. Can be usefull to display trace info in it if you want to modify the code.
						<br /><span class='underline'>Type</span>: Boolean
						<br /><span class='underline'>Default</span>: false
					</li>
				</ul>
				<br />
			</li>
			<li><strong>Callback</strong>
				<ul>
					<li><strong>load_callback</strong>: the function name that will be called when the user will press the "load" button in the toolbar. This function will reveice one parameter that will be the id of the textarea. You can update the content of the textarea by using "editAreaLoader.setValue(the_id, new_value);".
						<br /><span class='underline'>Type</span>: String
						<br /><span class='underline'>Default</span>: ""
					</li>
					<li><strong>save_callback</strong>: the function name that will be called when the user will press the "save" button in the toolbar. This function will reveice two parameters, the first being the id of the textarea and the second containing the content of the textarea.
						<br /><span class='underline'>Type</span>: String
						<br /><span class='underline'>Default</span>: ""
					</li>
					<li><strong>change_callback</strong>: the function name that will be called when the onchange event of the textarea of EditArea will be triggered. This function will reveice one parameter that will be the id of the textarea. Will be triggered only is EditArea is displayed.
						<br /><span class='underline'>Type</span>: String
						<br /><span class='underline'>Default</span>: ""
					</li>
					<li><strong>submit_callback</strong>: the function name that will be called when the form containing the EditArea will be submitted. This function will reveice one parameter that will be the id of the textarea. Will be triggered regardless the state of EditArea (displayed or not).
						<br /><span class='underline'>Type</span>: String
						<br /><span class='underline'>Default</span>: ""
					</li>
					<li><strong>EA_init_callback</strong>: the function name that will be called just after the editAreaLoader.init() function, once EditAreaLoader will be initalized but still not displayed. This function will receive one parameter that will be the id of the textarea.
						<br /><span class='underline'>Type</span>: String
						<br /><span class='underline'>Default</span>: ""
					</li>
					<li><strong>EA_delete_callback</strong>: the function name that will be called when EditArea will be destroyed regardless the fact that it has been displayed or not. This function will reveice one parameter that will be the id of the textarea.
						<br /><span class='underline'>Type</span>: String
						<br /><span class='underline'>Default</span>: ""
					</li>
					<li><strong>EA_toggle_on_callback</strong>: the function name that will be called when EditArea will be toogled on for. This function will reveice one parameter that will be the id of the textarea.
						<br /><span class='underline'>Type</span>: String
						<br /><span class='underline'>Default</span>: ""
					</li>
					<li><strong>EA_toggle_off_callback</strong>: the function name that will be called when EditArea will be toggled off. This function will reveice one parameter that will be the id of the textarea.
						<br /><span class='underline'>Type</span>: String
						<br /><span class='underline'>Default</span>: ""
					</li>
					<li><strong>EA_load_callback</strong>: the function name that will be called when EditArea will be displayed for the first time. This function will reveice one parameter that will be the id of the textarea.
						<br /><span class='underline'>Type</span>: String
						<br /><span class='underline'>Default</span>: ""
					</li>
					<li><strong>EA_unload_callback</strong>: the function name that will be called when EditArea will be destroyed (if it have been displayed at least one time).  This function will reveice one parameter that will be the id of the textarea.
						<br /><span class='underline'>Type</span>: String
						<br /><span class='underline'>Default</span>: ""
					</li>
					<li><strong>EA_file_switch_on_callback</strong>: the function name that will be called when the tabulation of the file will be selected. This function will reveice one parameter that will be an associative array containing all file's infos.
						<br /><span class='underline'>Type</span>: String
						<br /><span class='underline'>Default</span>: ""
					</li>
					<li><strong>EA_file_switch_off_callback</strong>: the function name that will be called when the tabulation of the file will be blur (the file was selected, and another file receive focus). This function will reveice one parameter that will be an associative array containing all file infos.
						<br /><span class='underline'>Type</span>: String
						<br /><span class='underline'>Default</span>: ""
					</li>
					<li><strong>EA_file_close_callback</strong>: the function name that will be called when the tabulation of a file will be closed. This function will reveice one parameter that will be an associative array containing all file infos. If the callback function return false, the file won't be closed.
						<br /><span class='underline'>Type</span>: String
						<br /><span class='underline'>Default</span>: ""
					</li>
				</ul>
				<br />
			</li>
		</ul>
		<div class="separator"></div>
		
		<h2>Initialization of EditArea</h2>
		<p>In order to initialize EditArea the following code must be placed within HEAD element
			of a document. The following example is configurated to convert the TEXTAREA element 
			which has &quot;textarea_1&quot; as id into editor when the page loads. The &quot;id&quot;
			option is the only obligatory option.
<pre>
&lt;html&gt;
&lt;head&gt;
<strong>&lt;script language=&quot;javascript&quot; type=&quot;text/javascript&quot; src=&quot;/editarea/edit_area/edit_area_full.js&quot;&gt;&lt;/script&gt;
&lt;script language=&quot;javascript&quot; type=&quot;text/javascript&quot;&gt;
editAreaLoader.init({
	id : &quot;textarea_1&quot;		// textarea id
	,syntax: "css"			// syntax to be uses for highgliting
	,start_highlight: true		// to display with highlight mode on start-up
});
&lt;/script&gt;</strong>
&lt;/head&gt;
</pre>
		</p>
		<p>See the <a href='include.html'>include</a> document to learn more about the way to use the best script include.
		</p>
		<p>If you want to convert several textarea on your webpage, just call several time the init function with a different id parameter.</p>

	</div>
	<div class='footer'>
		<div class="indexlink"><a href="index.html">Index</a></div>	
		<div class='copyright'>EditArea - &copy; Christophe Dolivet 2007-2008</div>
		<br style="clear: both" />
	</div>
</body>
</html>
