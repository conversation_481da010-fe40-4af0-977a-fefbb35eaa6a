<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en" >
<head>
<title>{$charmap_title}</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<link rel="stylesheet" type="text/css" href="css/charmap.css" />
<script language="Javascript" type="text/javascript" src="jscripts/map.js">
</script>
</head>
<body onload='map_load()'>
<div id='preview_code' class='preview'></div>
<div id='preview_char' class='preview'></div>
<h1>{$charmap_title}:</h1>
<select id='select_range' onchange='renderCharMapHTML()' title='{$charmap_choose_block}'>
</select>
<div id='char_list'>

</div>



</body>
</html>
