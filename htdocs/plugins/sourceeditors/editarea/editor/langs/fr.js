editAreaLoader.lang["fr"]={
new_document: "nouveau document (efface le contenu)",
search_button: "rechercher / remplacer",
search_command: "rechercher suivant / ouvrir la fen&ecirc;tre de recherche",
search: "rechercher",
replace: "remplacer",
replace_command: "remplacer / ouvrir la fen&ecirc;tre de recherche",
find_next: "rechercher",
replace_all: "tout remplacer",
reg_exp: "expr. r&eacute;guli&egrave;re",
match_case: "respecter la casse",
not_found: "pas trouv&eacute;.",
occurrence_replaced: "remplacements &eacute;ffectu&eacute;s.",
search_field_empty: "Le champ de recherche est vide.",
restart_search_at_begin: "Fin du texte atteint, poursuite au d&eacute;but.",
move_popup: "d&eacute;placer la fen&ecirc;tre de recherche",
font_size: "--<PERSON><PERSON> police--",
go_to_line: "aller &agrave; la ligne",
go_to_line_prompt: "aller a la ligne numero:",
undo: "annuler",
redo: "refaire",
change_smooth_selection: "activer/d&eacute;sactiver des fonctions d'affichage (meilleur affichage mais plus de charge processeur)",
highlight: "activer/d&eacute;sactiver la coloration syntaxique",
reset_highlight: "r&eacute;initialiser la coloration syntaxique (si d&eacute;syncronis&eacute;e du texte)",
word_wrap: "activer/d&eacute;sactiver les retours &agrave; la ligne automatiques",
help: "&agrave; propos",
save: "sauvegarder",
load: "charger",
line_abbr: "Ln",
char_abbr: "Ch",
position: "Position",
total: "Total",
close_popup: "fermer le popup",
shortcuts: "Racourcis clavier",
add_tab: "ajouter une tabulation dans le texte",
remove_tab: "retirer une tabulation dans le texte",
about_notice: "Note: la coloration syntaxique n'est pr&eacute;vue que pour de courts textes.",
toggle: "basculer l'&eacute;diteur",
accesskey: "Accesskey",
tab: "Tab",
shift: "Maj",
ctrl: "Ctrl",
esc: "Esc",
processing: "chargement...",
fullscreen: "plein &eacute;cran",
syntax_selection: "--Syntaxe--",
close_tab: "Fermer le fichier"
};
