editAreaLoader.lang["it"]={
new_document: "nuovo documento vuoto",
search_button: "cerca e sostituisci",
search_command: "trova successivo / apri finestra di ricerca",
search: "cerca",
replace: "sostituisci",
replace_command: "sostituisci / apri finestra di ricerca",
find_next: "trova successivo",
replace_all: "sostituisci tutti",
reg_exp: "espressioni regolari",
match_case: "confronta maiuscole/minuscole<br />",
not_found: "non trovato.",
occurrence_replaced: "occorrenze sostituite.",
search_field_empty: "Campo ricerca vuoto",
restart_search_at_begin: "Fine del testo raggiunta. Ricomincio dall'inizio.",
move_popup: "sposta popup di ricerca",
font_size: "-- Dimensione --",
go_to_line: "vai alla linea",
go_to_line_prompt: "vai alla linea numero:",
undo: "annulla",
redo: "ripeti",
change_smooth_selection: "abilita/disabilita alcune caratteristiche della visualizzazione",
highlight: "abilita/disabilita colorazione della sintassi",
reset_highlight: "aggiorna colorazione (se non sincronizzata)",
word_wrap: "toggle word wrapping mode",
help: "informazioni su...",
save: "salva",
load: "carica",
line_abbr: "Ln",
char_abbr: "Ch",
position: "Posizione",
total: "Totale",
close_popup: "chiudi popup",
shortcuts: "Scorciatoie",
add_tab: "aggiungi tabulazione",
remove_tab: "rimuovi tabulazione",
about_notice: "Avviso: la colorazione della sintassi vale solo con testo piccolo",
toggle: "Abilita/disabilita editor",
accesskey: "Accesskey",
tab: "Tab",
shift: "Shift",
ctrl: "Ctrl",
esc: "Esc",
processing: "In corso...",
fullscreen: "fullscreen",
syntax_selection: "--Syntax--",
close_tab: "Close file"
};
