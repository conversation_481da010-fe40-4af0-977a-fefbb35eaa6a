<?php
/**
 * Image Creation script
 * Xoops Frameworks addon
 *
 * based on Frameworks::captcha by <PERSON><PERSON> (phppp or D.J.) <<EMAIL>>
 *
 * @copyright	The XOOPS project http://www.xoops.org/
 * @license 	http://www.fsf.org/copyleft/gpl.html GNU public license
 * <AUTHOR> (phppp or D.J.) <<EMAIL>>
 * @since		XOOPS
 *
 * @category	ICMS
 * @package		Form
 * @subpackage	Elements
 * <AUTHOR> (phppp or D.J.) <<EMAIL>>
 * <AUTHOR> by <PERSON><PERSON> (aka stranger) <<EMAIL>>
 * @version		SVN: $Id: img.php 12340 2013-09-22 04:11:09Z skenow $
 */

include "../../../../../mainfile.php";
error_reporting(0);
icms::$logger->activated = FALSE;

$image_handler = icms::handler('icms_form_elements_captcha_Image');
$image_handler->loadImage();
