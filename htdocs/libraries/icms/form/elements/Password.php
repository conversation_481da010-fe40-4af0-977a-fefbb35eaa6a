<?php
//  ------------------------------------------------------------------------ //
//                XOOPS - PHP Content Management System                      //
//                    Copyright (c) 2000 XOOPS.org                           //
//                       <http://www.xoops.org/>                             //
//  ------------------------------------------------------------------------ //
//  This program is free software; you can redistribute it and/or modify     //
//  it under the terms of the GNU General Public License as published by     //
//  the Free Software Foundation; either version 2 of the License, or        //
//  (at your option) any later version.                                      //
//                                                                           //
//  You may not change or alter any portion of this comment or credits       //
//  of supporting developers from this source code or any supporting         //
//  source code which is considered copyrighted (c) material of the          //
//  original comment or credit authors.                                      //
//                                                                           //
//  This program is distributed in the hope that it will be useful,          //
//  but WITHOUT ANY WARRANTY; without even the implied warranty of           //
//  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the            //
//  GNU General Public License for more details.                             //
//                                                                           //
//  You should have received a copy of the GNU General Public License        //
//  along with this program; if not, write to the Free Software              //
//  Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA  02111-1307 USA //
//  ------------------------------------------------------------------------ //
// Author: Kazumi Ono (AKA onokazu)                                          //
// URL: http://www.myweb.ne.jp/, http://www.xoops.org/, http://jp.xoops.org/ //
// Project: The XOOPS Project                                                //
// ------------------------------------------------------------------------- //
/**
 * Creates a form password field
 *
 * @copyright	http://www.impresscms.org/ The ImpressCMS Project
 * @license		http://www.gnu.org/licenses/old-licenses/gpl-2.0.html GNU General Public License (GPL)

 * @category	ICMS
 * @package		Form
 * @subpackage	Elements
 * @version		$Id: Password.php 12313 2013-09-15 21:14:35Z skenow $
 */
defined('ICMS_ROOT_PATH') or die("ImpressCMS root path not defined");

/**
 * A password field
 *
 * @category	ICMS
 * @package 	Form
 * @subpackage 	Elements
 *
 * <AUTHOR> Ono	<<EMAIL>>
 * @copyright	copyright (c) 2000-2003 XOOPS.org
 */
class icms_form_elements_Password extends icms_form_Element {
	/**
	 * Size of the field.
	 * @var 		int
	 */
	private $_size;

	/**
	 * Maximum length of the text
	 * @var 		int
	 */
	private $_maxlength;

	/**
	 * Initial content of the field.
	 * @var 		string
	 */
	private $_value;

	/**
	 * Turns off the browser autocomplete function.
	 * @var 		boolean
	 */
	public  $autocomplete = false;

	/**
	 * Initial content of the field.
	 * @var 		string
	 */
	private $_classname;

	/**
	 * Constructor
	 *
	 * @param	string	$caption	Caption
	 * @param	string	$name		"name" attribute
	 * @param	int		$size		Size of the field
	 * @param	int		$maxlength	Maximum length of the text
	 * @param	int		$value		Initial value of the field.
	 * 							<b>Warning:</b> this is readable in cleartext in the page's source!
	 */
	public function __construct($caption, $name, $size, $maxlength, $value = '', $autocomplete = false, $classname = '') {
		$this->setCaption($caption);
		$this->setName($name);
		$this->_size = (int) ($size);
		$this->_maxlength = (int) ($maxlength);
		$this->setValue($value);
		$this->autoComplete = !empty($autocomplete);
		$this->setClassName($classname);
	}

	/**
	 * Get the field size
	 *
	 * @return	int
	 */
	public function getSize() {
		return $this->_size;
	}

	/**
	 * Get the max length
	 *
	 * @return	int
	 */
	public function getMaxlength() {
		return $this->_maxlength;
	}

	/**
	 * Get the "value" attribute
	 *
	 * @param	bool    $encode To sanitizer the text?
	 * @return	string
	 */
	public function getValue($encode = false) {
		return $encode ? htmlspecialchars($this->_value, ENT_QUOTES) : $this->_value;
	}

	/**
	 * Set the initial value
	 *
	 * @param	$value	string
	 */
	public function setValue($value) {
		$this->_value = $value;
	}

	/**
	 * Set the initial value
	 *
	 * @param	$value	string
	 */
	public function setClassName($classname) {
		$this->_classname = $classname;
	}

	/**
	 * Get the "class" attribute
	 *
	 * @param	bool    $encode To sanitizer the text?
	 * @return	string
	 */
	public function getClassName($encode = false) {
		return $encode ? htmlspecialchars($this->_classname, ENT_QUOTES) : $this->_classname;
	}

	/**
	 * Prepare HTML for output
	 *
	 * @return	string	HTML
	 */
	public function render() {

		$this->tpl = new icms_view_Tpl();
		$this->tpl->assign('ele_name', $this->getName());
		$this->tpl->assign('ele_class', $this->getClassName());
		$this->tpl->assign('ele_id', $this->getName());
		$this->tpl->assign('ele_size', $this->getSize());
		$this->tpl->assign('ele_maxlength', $this->getMaxlength());
		$this->tpl->assign('ele_value', $this->getValue());
		$this->tpl->assign('ele_extra', $this->getExtra());
		$this->tpl->assign('ele_autocomplete', $this->autoComplete);

		$element_html_template = $this->_customTemplate ? $this->_customTemplate : strtolower(static::class) . '_display.html';
		return $this->_tpl->fetch('db:' . $element_html_template);
	}
}
