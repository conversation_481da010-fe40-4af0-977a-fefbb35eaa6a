<?php
// $Id: xmltaghandler.php 19118 2010-03-27 17:46:23Z skenow $
/*******************************************************************************
    Location: <b>xml/XmlTagHandler</b><br>
     <br>
    XmlTagHandler<br>
    <br>
    Copyright &copy; 2001 eXtremePHP.  All rights reserved.<br>
    <br>
    <AUTHOR> <PERSON><PERSON><br>
*******************************************************************************/
/**
 * XML Parser Tag Handler
 *
 *
 /*******************************************************************************
 *   Location: <b>xml/XmlTagHandler</b>
 *
 *   XmlTagHandler

 *   @Copyright &copy; 2001 eXtremePHP.  All rights reserved.

 *   <AUTHOR> Egervari, Remi Michalski
 *******************************************************************************
 *
 * @package	Administration
 * @since	XOOPS
 * <AUTHOR> The XOOPS Project
 * <AUTHOR> by UnderDog <<EMAIL>>
 * @version	$Id: xmltaghandler.php 19118 2010-03-27 17:46:23Z skenow $
 */

class XmlTagHandler
{

	/****************************************************************************

	****************************************************************************/
	function XmlTagHandler()
	{
	}

	/****************************************************************************

	****************************************************************************/
	function getName()
	{
		return '';
	}

	/****************************************************************************

	****************************************************************************/
	function handleBeginElement(&$parser, &$attributes)
	{

	}

	/****************************************************************************

	****************************************************************************/
	function handleEndElement(&$parser)
	{

	}

	/****************************************************************************

	****************************************************************************/
	function handleCharacterData(&$parser,  &$data)
	{

	}
}

?>
