<?php
// formselectuser.php
// $Id: blauer-fisch $
define("_MA_USER_MORE", "搜索用户");
define("_MA_USER_REMOVE", "移除未选中的用户");

//%%%%%%	File Name findusers.php 	%%%%%
define("_MA_USER_ADD_SELECTED", "添加已选中的用户");

define("_MA_USER_GROUP", "组");
define("_MA_USER_LEVEL", "层");
define("_MA_USER_LEVEL_ACTIVE", "激活");
define("_MA_USER_LEVEL_INACTIVE", "未激活");
define("_MA_USER_LEVEL_DISABLED", "不可用");
define("_MA_USER_RANK", "级别");

define("_MA_USER_FINDUS","查找用户");
define("_MA_USER_REALNAME","真实姓名");
define("_MA_USER_REGDATE","加入日期");
define("_MA_USER_EMAIL","Email");
define("_MA_USER_PREVIOUS","上一页");
define("_MA_USER_NEXT","下一页");
define("_MA_USER_USERSFOUND","%s 用户已发现");

define("_MA_USER_ACTUS", "激活用户: %s");
define("_MA_USER_INACTUS", "未激活用户: %s");
define("_MA_USER_NOFOUND","没有发现用户");
define("_MA_USER_UNAME","用户名");
define("_MA_USER_ICQ","ICQ帐号");
define("_MA_USER_AIM","AIM帐号");
define("_MA_USER_YIM","YIM帐号");
define("_MA_USER_MSNM","MSNM帐号");
define("_MA_USER_LOCATION","定位包括");
define("_MA_USER_OCCUPATION","占有包括");
define("_MA_USER_INTEREST","兴趣包括");
define("_MA_USER_URLC","URL包含");
define("_MA_USER_SORT","排序依照");
define("_MA_USER_ORDER","次序");
define("_MA_USER_LASTLOGIN","上一次登录");
define("_MA_USER_POSTS","留言次数");
define("_MA_USER_ASC","按字母排序");
define("_MA_USER_DESC","按字母降序");
define("_MA_USER_LIMIT","每页用户数");
define("_MA_USER_RESULTS", "搜索结果");
define("_MA_USER_SHOWMAILOK", "显示用户类型");
define("_MA_USER_MAILOK","只显示能接受邮件的用户");
define("_MA_USER_MAILNG","只显示不能接受邮件的用户");
define("_MA_USER_BOTH", "显示全部");

define("_MA_USER_RANGE_LAST_LOGIN","<span style='color:#ff0000;'>X</span>天前登录");
define("_MA_USER_RANGE_USER_REGDATE","<span style='color:#ff0000;'>X</span>天前注册");
define("_MA_USER_RANGE_POSTS","留言");

define("_MA_USER_HASAVATAR", "使用头像");
define("_MA_USER_MODE_SIMPLE", "简单模式");
define("_MA_USER_MODE_ADVANCED", "高级模式");
define("_MA_USER_MODE_QUERY", "查询模式");
define("_MA_USER_QUERY", "查询");

define("_MA_USER_SEARCHAGAIN", "再次搜索");
define("_MA_USER_NOUSERSELECTED", "没有用户被选中");
define("_MA_USER_USERADDED", "用户已添加");

define("_MA_USER_SENDMAIL","发送邮件");
?>