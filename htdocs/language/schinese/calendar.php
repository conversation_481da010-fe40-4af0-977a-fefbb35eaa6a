<?php
// $Id: blauer-fisch $
//%%%%%		Time Zone	%%%%
define("_CAL_SUNDAY", "星期天");
define("_CAL_MONDAY", "星期一");
define("_CAL_TUESDAY", "星期二");
define("_CAL_WEDNESDAY", "星期三");
define("_CAL_THURSDAY", "星期四");
define("_CAL_FRIDAY", "星期五");
define("_CAL_SATURDAY", "星期六");
define("_CAL_JANUARY", "一月");
define("_CAL_FEBRUARY", "二月");
define("_CAL_MARCH", "三月");
define("_CAL_APRIL", "四月");
define("_CAL_MAY", "五月");
define("_CAL_JUNE", "六月");
define("_CAL_JULY", "七月");
define("_CAL_AUGUST", "八月");
define("_CAL_SEPTEMBER", "九月");
define("_CAL_OCTOBER", "十月");
define("_CAL_NOVEMBER", "十一月");
define("_CAL_DECEMBER", "十二月");
define("_CAL_TGL1STD", "设置每周的第一天");
define("_CAL_PREVYR", "上一. 年");
define("_CAL_PREVMNTH", "上一. 月");
define("_CAL_GOTODAY", "跳到今天");
define("_CAL_NXTMNTH", "下一月");
define("_CAL_NEXTYR", "下一年");
define("_CAL_SELDATE", "选择日期");
define("_CAL_DRAGMOVE", "拖拽移动");
define("_CAL_TODAY", "今天");
define("_CAL_DISPM1ST", "首先显示星期一");
define("_CAL_DISPS1ST", "首先显示星期天");

############# added since 1.1.2 #############
define("_CAL_SUN", "日");
define("_CAL_MON", "一");
define("_CAL_TUE", "二");
define("_CAL_WED", "三");
define("_CAL_THU", "四");
define("_CAL_FRI", "五");
define("_CAL_SAT", "六");
// First day of the week. "0" means display Sunday first, "1" means display Monday first, etc...
define("_CAL_FIRSTDAY", "0");
define("_CAL_JAN", "一月");
define("_CAL_FEB", "二月");
define("_CAL_MAR", "三月");
define("_CAL_APR", "四月");
define("_CAL_JUN", "六月");
define("_CAL_JUL", "七月");
define("_CAL_AUG", "八月");
define("_CAL_SEP", "九月");
define("_CAL_OCT", "十月");
define("_CAL_NOV", "十一月");
define("_CAL_DEC", "十二月");
// Direction of the calendar, ltr for left to right and rtl for roght to left (for Persian, Arabic, etc.)
define("_CAL_DIRECTION", "ltr");
define("_CAL_AM", "上午");
define("_CAL_AM_CAPS", "上午");
define("_CAL_PM", "下午");
define("_CAL_PM_CAPS", "下午");
define("_CAL_TIME", "时间");
define("_CAL_WK", "周"); // shorten of week-end
// This may be local-dependent.  It specifies the week-end days, as an array
// of comma-separated numbers.  The numbers are from 0 to 6: 0 means Sunday, 1
// means Monday, etc.
define("_CAL_WEEKEND", "0,6");
define("_CAL_DSPFIRST", "首先显示 %s ");
//This constants are for the jalali calendar included in the library
define('_CAL_FARVARDIN','Farvardin');
define('_CAL_ORDIBEHESHT','Ordibehest');
define('_CAL_KHORDAD','Khordad');
define('_CAL_TIR','Tir');
define('_CAL_MORDAD','Mordad');
define('_CAL_SHAHRIVAR','Shahrivar');
define('_CAL_MEHR','Mehr');
define('_CAL_ABAN','Aban');
define('_CAL_AZAR','Azar');
define('_CAL_DEY','Day');
define('_CAL_BAHMAN','Bahman');
define('_CAL_ESFAND','Esfand');
define("_CAL_NUMS_ARRAY", "'0', '1', '2', '3', '4', '5', '6', '7', '8', '9'"); // numeric values can differ in different languages
define("_CAL_TT_DATE_FORMAT", "%a, %b %e");
############# added since 1.2 #############
define("_CAL_SUFFIX", "th");
define('_CAL_AM_LONG','上午');
define('_CAL_PM_LONG','下午');
?>