<?php
// $Id: blauer-fisch $ 
//%%%%%%	File Name readpmsg.php 	%%%%%
define("_PM_DELETED","信息已被删除");
define("_PM_PRIVATEMESSAGE","私人信息");
define("_PM_INBOX","信箱");
define("_PM_FROM","从");
define("_PM_YOUDONTHAVE","没有任何私人信息");
define("_PM_FROMC","从: ");
define("_PM_SENTC","发送到: "); // The date of message sent
define("_PM_PROFILE","简介");

// %s is a username
define("_PM_PREVIOUS","上一信息");
define("_PM_NEXT","下一信息");

//%%%%%%	File Name pmlite.php 	%%%%%
define("_PM_SORRY","对不起! 你不是注册用户.");
define("_PM_REGISTERNOW","现在注册!");
define("_PM_GOBACK","返回");
define("_PM_USERNOEXIST","数据库中找不到所选的用户");
define("_PM_PLZTRYAGAIN","请检查用户名，再试一次");
define("_PM_MESSAGEPOSTED","信息已发布");
define("_PM_CLICKHERE","点击这里查看你的私人信息");
define("_PM_ORCLOSEWINDOW","点击这里关闭本窗口");
define("_PM_USERWROTE","%s 写入:");
define("_PM_TO","至: ");
define("_PM_SUBJECTC","主题: ");
define("_PM_MESSAGEC","信息: ");
define("_PM_CLEAR","清除");
define("_PM_CANCELSEND","取消发送");
define("_PM_SUBMIT","提交");

//%%%%%%	File Name viewpmsg.php 	%%%%%
define("_PM_SUBJECT","主题");
define("_PM_DATE","日期");
define("_PM_NOTREAD","未读");
define("_PM_SEND","发送");
define("_PM_DELETE","删除");
define("_PM_REPLY", "回复");
define("_PM_PLZREG","请注册，以发送私人信息!");

define("_PM_ONLINE", "在线");

define("_PM_MESSAGEPOSTED_EMAILSUBJ","[%s] 私人信息通知");
?>