<?php
// $Id: search.php 12229 2013-07-19 08:08:05Z fiammy $
// $Id: search.php 12229 2013-07-19 08:08:05Z fiammy $
//%%%%%%	File Name search.php 	%%%%%
define("_SR_SEARCH","Search");
define("_SR_PLZENTER","Please enter all required data!");
define("_SR_SEARCHRESULTS","Search Results");
define("_SR_NOMATCH","No Match Found for your Query");
//define("_SR_FOUND","Found <b>%s</b> match(es)");
define("_SR_SHOWING","(Showing %d - %d)");
define("_SR_ANY","Any (OR)");
define("_SR_ALL","All (AND)");
define("_SR_EXACT","Exact Match");
define("_SR_SHOWALLR","Show all results");
define("_SR_NEXT","Next");
define("_SR_PREVIOUS","Previous");
define("_SR_KEYWORDS","Keyword(s)");
define("_SR_TYPE","Search Type");
define("_SR_SEARCHIN","Search in");
define('_SR_KEYTOOSHORT', 'Keywords must be at least <b>%s</b> characters long');
define('_SR_KEYIGNORE', 'Keywords shorter than <b>%s</b> characters will be ignored');
define('_SR_SEARCHRULE', 'Search Rule');
define('_SR_IGNOREDWORDS', 'The following words are shorter than allowed minimum length (%u chars) and were not included in your search:');
############# added since 1.2 #############
define('_SR_HITSRETURNED', 'hit(s) returned');
?>