<?php
// $Id: search.php 9536 2009-11-13 18:59:32Z pesianstranger $
//%%%%%%	File Name search.php 	%%%%%
define("_SR_SEARCH","Zoek");
define("_SR_PLZENTER","Vul alle verplichten velden in!");
define("_SR_SEARCHRESULTS","Zoek resulta(a)t(en)");
define("_SR_NOMATCH","Geen resultaten gevonden");
//define("_SR_FOUND","Er zijn <b>%s</b> resultaten gevonden");
define("_SR_SHOWING","(<PERSON><PERSON><PERSON><PERSON><PERSON> %d - %d)");
define("_SR_ANY","Elke (OR)");
define("_SR_ALL","Alle (AND)");
define("_SR_EXACT","Exact resultaat");
define("_SR_SHOWALLR","Laat alle resultaten zien");
define("_SR_NEXT","Volgende");
define("_SR_PREVIOUS","Vorige");
define("_SR_KEYWORDS","Sleutelwoorden");
define("_SR_TYPE","Type");
define("_SR_SEARCHIN","Zoeken in");
define('_SR_KEYTOOSHORT', "Zoekwoorden moeten langer zijn dan <b>%s</b> karakters!");
define('_SR_KEYIGNORE', "Zoekwoorden korter dan <b>%s</b> karakters worden genegeerd");
define('_SR_SEARCHRULE', 'Zoek Regel');
define('_SR_IGNOREDWORDS', 'De volgende woorden waren korter dan het minimum aantal %u karakters en zijn daarom in uw zoekopdracht genegeerd:');
############# added since 1.2 #############
define('_SR_HITSRETURNED', 'hit(s) teruggekeerd');
?>