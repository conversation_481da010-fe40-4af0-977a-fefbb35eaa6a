<?php

// g�n�ralit�s
define("THEME_UPTOP",					"Haut de Page");
define("THEME_VALIDCSS",				"Ce site est valide W3C pour le CSS 1 et 2");
define("THEME_VALIDXHTML",				"Ce site est valide W3C pour le XHTML 1.01");
define("THEME_LICENSE","Voir Creative Commons Licence");
define("THEME_W3C","Valid W3C - XHTML 1.01 and CSS 1/2/3");

// userbar.html
define("THEME_REGISTER",				"Enregistrer");
define("THEME_REISTERATION",		"Enregistrement?");
define("THEME_REMEMBER",				"Se rappeller de moi ?");
define("THEME_PSEUDO",					"Pseudo");
define("THEME_DESC_PSEUDO",				"Saisissez votre Pseudo");
define("THEME_PASSWORD",				"Mot de passe");
define("THEME_DESC_PASSWORD",			"Saisissez votre mot de passe");

define("THEME_WELLCOME",  "Bienvenue,");
define("THEME_PROFILE",   "Voir votre profil");
define("THEME_NOTIFICATION","Bookmarks");
define("THEME_MESSAGE","Afficher le contenu de votre messagerie");
define("THEME_INBOX","Messagerie");
define("THEME_YOUHAVE","Vous avez ");
define("THEME_NOTREAD","message(s) non lus");
define("THEME_MYACCOUNT","Mon profil");
define("THEME_LOGOUT","D�connecter");
define("THEME_ADMIN","Administration");
define("THEME_OR","ou");

//recherche sur le site/searchbar.html
define("THEME_SEARCH",				  "Rechercher sur le site");
define("THEME_DESC_SEARCH",			  "moteur de recherche");
define("THEME_KEYWORDS",			  "Saisissez les mots-cl�s");

// globalnav.html
define("THEME_CONTACT",			  "Contact");
define("THEME_CONTACT_DESC",	"Nous Contacter");
define("THEME_SITEMAP",			  "Mappe du site");
define("THEME_RSS",			  "RSS");

define("THEME_NAVTITLE",			  "Navigation");
define("THEME_HOME",				  "Accueil");
define("THEME_HOME_DESC",			  "Revenir � l'accueil");

define("THEME_MODULE1","Nouvelles");
define("THEME_MODULE1_DESC","Voir les nouvelles");

define("THEME_MODULE1_SUB1","Category 1"); //*** the SUB are for sub-menu in globalnav.html 'dynamic'
define("THEME_MODULE1_SUB2","Category 2"); //*** the SUB are for sub-menu in globalnav.html 'dynamic'
define("THEME_MODULE1_SUB3","Category 3"); //*** the SUB are for sub-menu in globalnav.html 'dynamic'
define("THEME_MODULE1_SUB4","Category 4"); //*** the SUB are for sub-menu in globalnav.html 'dynamic'
define("THEME_MODULE1_SUB5","Category 5"); //*** the SUB are for sub-menu in globalnav.html 'dynamic'

define("THEME_MODULE2","Forum");
define("THEME_MODULE2_DESC","Visiter nos forums");

define("THEME_MODULE3","Gallery");
define("THEME_MODULE3_DESC","Visiter notre gallerie");

define("THEME_MODULE3_SUB1","Gallerie 1"); //*** the SUB are for sub-menu in globalnav.html 'dynamic'
define("THEME_MODULE3_SUB2","Gallerie 2"); //*** the SUB are for sub-menu in globalnav.html 'dynamic'
define("THEME_MODULE3_SUB3","Gallerie 3"); //*** the SUB are for sub-menu in globalnav.html 'dynamic'
define("THEME_MODULE3_SUB4","Gallerie 4"); //*** the SUB are for sub-menu in globalnav.html 'dynamic'
define("THEME_MODULE3_SUB5","Gallerie 5"); //*** the SUB are for sub-menu in globalnav.html 'dynamic'

define("THEME_MODULE4","Films");
define("THEME_MODULE4_DESC","Voir nos films");

define("THEME_MODULE4_SUB1","Film 1");
define("THEME_MODULE4_SUB2","Film 2"); //*** the SUB are for sub-menu in globalnav.html 'dynamic'
define("THEME_MODULE4_SUB3","Film 3"); //*** the SUB are for sub-menu in globalnav.html 'dynamic'
define("THEME_MODULE4_SUB4","Film 4"); //*** the SUB are for sub-menu in globalnav.html 'dynamic'
define("THEME_MODULE4_SUB5","Film 5"); //*** the SUB are for sub-menu in globalnav.html 'dynamic'

define("THEME_MODULE5","Liens sur le web");
define("THEME_MODULE5_DESC","Visiter nos liens");

define("THEME_MODULE6","t�l�chargements");
define("THEME_MODULE6_DESC","T�l�charger nos fichiers");

define("THEME_MODULE7","Contenu");
define("THEME_MODULE7_DESC","Utiliser le module Contenu");

define("THEME_MODULE8","Protector");
define("THEME_MODULE8_DESC","Utiliser Protector Module");

// Theme accessibility
define("THEME_Aa","Aa");
define("THEME_Amin","-");
define("THEME_Aplus","+");

// ImpressCMS Description Links
define("THEME_Support_DESC","Soutenez le projet ImpressCMS!");
define("THEME_Blogs_DESC","Lire les blogs ImpressCMS");
define("THEME_Wiki_DESC","Learn more in the community-driven wiki for ImpressCMS");
define("THEME_Addons_DESC","T�l�charger des modules, des th�mes et plus de documentation");
define("THEME_Community_DESC","Rejoinger la communaut� ImpressCMS");
define("THEME_ImpressCMS_DESC","Visiter le site pricipal ImpressCMS");
define("THEME_FEEDS","Flux RSS");
define("THEME_PARTNERS","Partenaires");
?>