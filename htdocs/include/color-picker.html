<!--
Title: Tigra Color Picker
URL: http://www.softcomplex.com/products/tigra_color_picker/
Version: 1.1
Date: 06/26/2003 (mm/dd/yyyy)
Note: Permission given to use this script in ANY kind of applications if
   header lines are left unchanged.
Note: Script consists of two files: picker.js and picker.html
-->

<html>
<head>
<title>Tigra Color Picker</title>
<style>
.bd {
	border: 1px inset InactiveBorder;
}

.s {
	width: 181
}
</style>
</head>
<body leftmargin="5" topmargin="5" marginheight="5" marginwidth="5"
	onload="P.C(P.initPalette)">
<table cellpadding=0 cellspacing=2 border=0 width=184>
	<form>
	<tr>
		<td align="center"><script language="JavaScript">
	var P = opener.TCP;
	onload = "P.show(P.initPalette)";
	P.draw(window, document);
</script></td>
	</tr>
	</form>
</table>
</body>
</html>
