<?php
// $Id: blauer-fisch $
//%%%%%%	File Name findusers.php 	%%%%%

define("_AM_FINDUS","查找用户");
define("_AM_AVATAR","头像");
define("_AM_REALNAME","真实姓名");
define("_AM_REGDATE","注册日期");
define("_AM_EMAIL","电子邮箱");
define("_AM_PM","私人信息");
define("_AM_URL","URL");
define("_AM_PREVIOUS","上一页");
define("_AM_NEXT","下一页");
define("_AM_USERSFOUND","已找到%s个用户");

define("_AM_ACTUS", "已激活用户: %s");
define("_AM_INACTUS", "未激活用户: %s");
define("_AM_NOFOUND","没有发现任何用户");
define("_AM_UNAME","用户名");
define("_AM_ICQ","ICQ");
define("_AM_AIM","AIM");
define("_AM_YIM","YIM");
define("_AM_MSNM","MSN");
define("_AM_LOCATION","来自");
define("_AM_OCCUPATION","所在地");
define("_AM_INTEREST","兴趣");
define("_AM_URLC","URL包含");
define("_AM_LASTLOGMORE","上次登录时间大于<span style='color:#ff0000;'>X</span>天前");
define("_AM_LASTLOGLESS","上次等于时间小于<span style='color:#ff0000;'>X</span>天前");
define("_AM_REGMORE","注册时间大于<span style='color:#ff0000;'>X</span>天前");
define("_AM_REGLESS","注册时间小于<span style='color:#ff0000;'>X</span>天前");
define("_AM_POSTSMORE","留言数量大于<span style='color:#ff0000;'>X</span>");
define("_AM_POSTSLESS","留言数量小于<span style='color:#ff0000;'>X</span>");
define("_AM_SORT","分类按");
define("_AM_ORDER","排序");
define("_AM_LASTLOGIN","上次登录");
define("_AM_POSTS","留言数量");
define("_AM_ASC","按字母排序");
define("_AM_DESC","按字母降序");
define("_AM_LIMIT","每页显示用户");
define("_AM_RESULTS", "查找结果");
define("_AM_SHOWMAILOK", "接收邮件");
define("_AM_MAILOK","仅能接收邮件的用户");
define("_AM_MAILNG","仅不能接收邮件的用户");
define("_AM_SHOWTYPE", "激活");
define("_AM_ACTIVE","仅已激活用户");
define("_AM_INACTIVE","仅未激活用户");
define("_AM_BOTH", "全部用户");
define("_AM_SENDMAIL", "发送邮件");
define("_AM_ADD2GROUP", "添加用户到 %s 组");
define("_AM_GROUPS", "群组");

######################## Added in 1.2 ###################################
define("_AM_LOGINNAME","登录名");

######################## Added in 1.3 ###################################
define("_AM_ACTIONS","Actions");