@import url(layout.css);
@import url(../blocks.css);
@import url(../content.css);


/* ------ General styles ------ */

html, body {
	color: #000;
	background: #eae0b7;
	margin: 0;
	padding: 0;
	font-family: Tahoma, Arial, Helvetica, sans-serif;
	font-size: 10pt;
}

#icms-canvas {
	border-top: 2px solid #DDDDFF;
	border-left: 2px solid #DDDDFF;
	border-right: 2px solid #DDDDFF;
	background: #FFF;
}

table {
	width: 100%;
}

table td {
	padding: 0;
	border-width: 0;
	vertical-align: top;
}

img {
	border: 0;
}

a {
	color: #666;
	text-decoration: none;
	font-weight: bold;
}

a:hover {
	color: #8e8e8e;
}

h1 {font-size: 1.4em; }
h2 {font-size: 1.2em;}
h3 {font-size: 1em;}
h4 {font-size: 1em;}
h5 {font-size: .9em;}
h6 {font-size: .8em;}

/* ------ Header rules ------ */

#icms-header {
	clear: both;
	height: 80px;
	position: relative;
	background: url(../img/bghead.png) repeat-x;
}

#icms-headerlogo {
	float: right;
}

#icms-headerbanner {
	position:relative;
	top:10px;
	left:20px;
	float: left;
}

/* ------ Header top menu navigation rules ------ */

#icms-globalnav {
	border-bottom: 1px solid #ddd;
	background-image: url(../img/hbar.gif);
	clear: both;
	height: 20px;
	padding-right: 11px;
}

#icms-globalnav ul {
	list-style-type: none;
	margin: 0px;
	padding: 2px 0 0 0;
	float: right;
}

#icms-globalnav li {
	display: inline;
}

#icms-globalnav li a {
	float: left;
	text-decoration: none;
	text-align: center;
	display: block;
	padding-right: 20px;
}

#icms-globalnav li a:hover {
}

/* ---------- Column left/right rules ---------- */

#icms-canvas-rightcolumn {}
#icms-canvas-rightcolumn a {}
#icms-canvas-rightcolumn a:hover {}

#icms-canvas-leftcolumn {}
#icms-canvas-leftcolumn a {}
#icms-canvas-leftcolumn a:hover {}

/* ------ Main content ------ */

#icms-canvas-content {	
	line-height: 1.3em;
}

/* lists */
#icms-canvas-content ul {margin: 5px; padding: 2px; list-style: decimal inside; text-align: left;}
#icms-canvas-content li {margin-left: 5px; color: #000; background-color: inherit; line-height: 1.4em; list-style: circle;}

#icms-page {
	padding: 0 .5em;
}

/* ---------- Module display rules ---------- */

#icms-content {
	padding: 15px 1%;
}

/* ---------- Redirect ------------ */
#icms-redirect {
	margin: 40px 0;
	margin-left: auto;
	margin-right: auto;
	width: 85%;
	background-color: #f7e6bd;
	color: #222;
	text-align: center;
	font-weight: bold;
	padding: 10px;
	text-align: center;
	border: 2px solid #DDDDFF;
}
/* ---------- Site close ------------ */
#icms-siteclose-login {
	width: 270px;
	margin-left: auto;
	margin-right: auto; 	
	border:1px solid #DDDDFF;	
	margin-top: 40px;
	margin-bottom: 20px;
}
#icms-siteclose-login h2 {
	background-color: #f3ac03;
	font-weight: bold;
	font-size: 1em; 
	color: white;
	padding: 2px;	
	margin-top: 0px;
	margin-bottom: 0px;
	text-align: center;	
}
#icms-siteclose-login form {
	padding: 0px;
	margin: 0px;	
}
#icms-siteclose-login div{
	padding: 6px;
}
#icms-siteclose-login span.left {
	float: left;
	padding-bottom: 3px;
}
#icms-siteclose-login span.right {
	float: right;
	padding-right: 3px;
	padding-bottom: 3px;
}
#icms-siteclose-login div.button {
	text-align: right;
	clear: both;	
	padding-right: 0px;
}
/* ---------- Footer rules ---------- */

#icms-footer {
	text-align:			center;
	font-size:			90%;
	color:				#666;
	margin:				0px;
	background-image: url(../img/hbar.gif);
        background-repeat: repeat-x;
	clear: both;
	height: 20px;
}

#icms-footer a {
	color:				#333;
}

/* ----------  mode debug  ---------- */
/* This is also in xoops.css and this is generated in the logger class */
#xo-logger-output {
	font-size:			80%;
}
.outer { direction : rtl;}
