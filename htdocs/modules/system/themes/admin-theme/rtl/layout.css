/* ---------- Canvas rules ---------- */

#icms-canvas {
	min-width: 850px;
	width: 98%;
	margin: 10px auto 0px;
}

#icms-canvas-content {}

#icms-canvas-columns {
	margin:		35px 0px 0px 24px;
}

#icms-canvas-leftcolumn {
	float:			left;
	width:			170px;
	margin-right:	-170px;
}

* html #icms-canvas-leftcolumn {
	margin-left:	-85px;
}

#icms-canvas-rightcolumn {
	float:			left;
	width:			170px;
	margin-left:	-170px;
}

* html #icms-canvas-rightcolumn {
	margin-left:	-90px;
	margin-right:	-90px;
}

/* ----------Canvas rules (when columns are visible) ---------- */

#icms-canvas.leftcolumn-layout #icms-canvas-columns, #icms-canvas.threecolumns-layout #icms-canvas-columns { padding-right: 170px; }
#icms-canvas.rightcolumn-layout #icms-canvas-columns, #icms-canvas.threecolumns-layout #icms-canvas-columns {	padding-left: 190px; }
 #icms-footer { clear: both;}

/* ----------Page [center column] rules ---------- */

#icms-page {
	float:			left; 
	width:			97%;
}

/* ------- Top and bottom zones (2 columns) ------- */

#icms-page-topleftblocks, #icms-page-bottomleftblocks {
	float:			left;
	width:			48%;
	margin-left:	1%;
	margin-right:	1%;
	padding:			0px;
}
#icms-page-toprightblocks, #icms-page-bottomrightblocks {
	float:			right;
	width:			48%;
	margin-left:	1%;
	margin-right:	1%;
}
* html #icms-page-topleftblocks, * html #icms-page-bottomleftblocks,
* html #icms-page-toprightblocks, * html #icms-page-bottomrightblocks
{
	width:			47%;
}

#icms-page-topcenterblocks, #icms-page-bottomcenterblocks {
	clear:			both;
	margin-left:	1%;
	margin-right:	1%;
}
/* ------- Top and bottom zones (3 columns) ------- */

#icms-page-topblocks.icms-lcrpageblocks .icms-blockszone, #icms-page-bottomblocks.icms-lcrpageblocks .icms-blockszone {
	float:			right;
	clear:			none;
	width:			31.2%;
}

* html #icms-page-topblocks.icms-lcrpageblocks .icms-blockszone,
* html #icms-page-bottomblocks.icms-lcrpageblocks .icms-blockszone {
	width:			30.6%;
}

#icms-page-topblocks.icms-lcrpageblocks #icms-page-topleftblocks {}


#icms-content {	
	position:		relative; 
	clear: 			none;
}
