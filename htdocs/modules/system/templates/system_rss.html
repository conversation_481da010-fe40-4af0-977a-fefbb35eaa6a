<?xml version="1.0" encoding="UTF-8"?>
<rss version="2.0">
  <channel>
    <title><{$channel_title}></title>
    <link><{$channel_link}></link>
    <description><{$channel_desc}></description>
	<copyright><{$channel_copyright}></copyright>
    <lastBuildDate><{$channel_lastbuild}></lastBuildDate>
    <docs>http://backend.userland.com/rss/</docs>
    <generator><{$channel_generator}></generator>
    <category><{$channel_category}></category>
    <managingEditor><{$channel_editor}></managingEditor>
    <webMaster><{$channel_webmaster}></webMaster>
	<ttl><{$channel_ttl}></ttl>
    <language><{$channel_language}></language>
    <{if $image_url != ""}>
    <image>
      <title><{$channel_title}></title>
      <url><{$image_url}></url>
      <link><{$channel_link}></link>
      <width><{$channel_width}></width>
      <height><{$channel_height}></height>
    </image>
    <{/if}>
    <{foreach item=item from=$items}>
    <item>
      <title><{$item.title}></title>
      <link><{$item.link}></link>
      <description><{$item.description}></description>
      <pubDate><{$item.pubdate}></pubDate>
      <guid><{$item.guid}></guid>
	  <category><{$item.category}></category>
	  <author><{$item.author}></author>
    </item>
    <{/foreach}>
  </channel>
</rss>