<div class="CPbigTitle" style="background-image: url(<{$xoops_url}>/modules/system/admin/modulesadmin/images/modulesadmin_big.png)"><{$lang_madmin}></div><br />
<h2><{$lang_installed}></h2>
<form action='admin.php' method='post' name='moduleadmin' id='moduleadmin'>
<table width="100%" cellpadding="4" cellspacing="1" border="0" class="outer">
  <tr align='center' valign='middle'>
    <th width="40%"><{$lang_module}></th>
    <th><{$lang_version}></th>
    <th><{$lang_modstatus}></th>
    <th><{$lang_lastup}></th>
    <th><{$lang_active}></th>
    <th><{$lang_order}><br /><small><{$lang_order0}></small></th>
    <th width='130px'><{$lang_action}></th>
  </tr>
  <{foreach item=module from=$modules}>
    <tr valign='middle'class="<{cycle values="even,odd"}>">
      <td align="<{$smarty.const._GLOBAL_LEFT}>" valign="middle">
        <div id="modlogo" style="float: <{$smarty.const._GLOBAL_LEFT}>; padding: 2px;">
          <{if $module.hasadmin == 1 && $module.isactive == '1'}>
            <a href="<{$xoops_url}>/modules/<{$module.dirname}>/<{$module.adminindex}>">
              <img src="<{$xoops_url}>/modules/<{$module.dirname}>/<{$module.image}>" alt="<{$module.name}>" title="<{$module.name}>" border="0" />
            </a>
          <{else}>
            <img src="<{$xoops_url}>/modules/<{$module.dirname}>/<{$module.image}>" alt="<{$module.name}>" title="<{$module.name}>" border="0" />
          <{/if}>&nbsp;
        </div>
        <div id="modlogo" style="float: <{$smarty.const._GLOBAL_LEFT}>; padding-top: 2px;">
          <b><{$lang_modulename}>: </b><{$module.name}><br />
          <b><{$lang_moduletitle}>: </b><input type="text" name="newname[<{$module.mid}>]" value="<{$module.title}>" maxlength="150" size="30" />
        </div>
        <input type="hidden" name="oldname[<{$module.mid}>]" value="<{$module.title}>" />
      </td>
      <td align='center' valign="middle"><{$module.version}></td>
      <td align='center' valign="middle"><{$module.status}></td>
      <td align='center' valign="middle"><{$module.last_update}></td>
      <td align='center' valign="middle">
        <{if $module.dirname == 'system'}>
          <input type="hidden" name="newstatus[<{$module.mid}>]" value="1" />
          <input type="hidden" name="oldstatus[<{$module.mid}>]" value="1" />
        <{else}>
          <{if $module.isactive == '1'}>
            <input type="checkbox" name="newstatus[<{$module.mid}>]" value="1" checked="checked" />
            <input type="hidden" name="oldstatus[<{$module.mid}>]" value="1" />
          <{else}>
            <input type="checkbox" name="newstatus[<{$module.mid}>]" value="1" />
            <input type="hidden" name="oldstatus[<{$module.mid}>]" value="0" />
          <{/if}>
        <{/if}>
      </td>
      <td align='center' valign="middle">
        <{if $module.hasmain == '1'}>
          <input type="hidden" name="oldweight[<{$module.mid}>]" value="<{$module.weight}>" />
          <input type="text" name="weight[<{$module.mid}>]" size="3" maxlength="5" value="<{$module.weight}>" />
        <{else}>
          <input type="hidden" name="oldweight[<{$module.mid}>]" value="0" />
          <input type="hidden" name="weight[<{$module.mid}>]" value="0" />
        <{/if}>
      </td>
      <td align='center' valign="middle">
        <{if $module.support_site_url != '' &&  $module.isactive == '1'}>
          <a href="<{$module.support_site_url}>" rel="external"><img src="<{$xoops_url}>/modules/system/images/support.png" alt="<{$lang_support}>" title="<{$lang_support}>"/></a>
        <{/if}>
        <a href="<{$xoops_url}>/modules/system/admin.php?fct=modulesadmin&amp;op=update&amp;module=<{$module.dirname}>"><img src="<{$xoops_url}>/modules/system/images/update.png" alt="<{$lang_update}>" title="<{$lang_update}>"/></a>
        <{if $module.isactive != '1'}>
          <a href="<{$xoops_url}>/modules/system/admin.php?fct=modulesadmin&amp;op=uninstall&amp;module=<{$module.dirname}>"><img src="<{$xoops_url}>/modules/system/images/uninstall.png" alt="<{$lang_unistall}>" title="<{$lang_unistall}>" /></a>
        <{/if}>  
        <a href='javascript:openWithSelfMain("<{$xoops_url}>/modules/system/admin.php?fct=version&amp;mid=<{$module.mid}>","Info",300,230);'><img src="<{$xoops_url}>/modules/system/images/info.png" alt="<{$lang_info}>" title="<{$lang_info}>" /></a>
        <input type="hidden" name="module[]" value="<{$module.mid}>" />
      </td>
    </tr>
  <{/foreach}>
  <tr class='foot'>
    <td colspan='7' align='center'>
      <input type='hidden' name='fct' value='modulesadmin' />
      <input type='hidden' name='op' value='confirm' />
      <input type='submit' name='submit' value='<{$lang_submit}>' />
    </td>
  </tr>
</table>
</form>
<br />
<h2><{$lang_noninstall}></h2>
<table width='100%' border='0' class='outer' cellpadding='4' cellspacing='1'>
  <tr align='center'>
    <th><{$lang_module}></th>
    <th><{$lang_version}></th>
    <th><{$lang_modstatus}></th>
    <th width='130px'><{$lang_action}></th>
  </tr>
  <{foreach item=module from=$avmodules}>
    <tr valign='middle'class="<{cycle values="even,odd"}>">
      <td>
        <div id="modlogo" style="padding: 2px;"><img src="<{$xoops_url}>/modules/<{$module.dirname}>/<{$module.image}>" alt="<{$module.name}>" alt="<{$module.name}>" border="0" />&nbsp;</div>
	    <div id="modlogo" style="padding-top: 10px;"> <b><{$lang_modulename}>: </b><{$module.name}><br /> </div>
      </td>
      <td align='center'><{$module.version}></td>
      <td align='center'><{$module.status}></td>
      <td width='130px' align='center'>
        <a href="<{$xoops_url}>/modules/system/admin.php?fct=modulesadmin&op=install&module=<{$module.dirname}>"><img src="<{$xoops_url}>/modules/system/images/install.png" alt="<{$lang_install}>" title="<{$lang_install}>" /></a>
        <a href='javascript:openWithSelfMain("<{$xoops_url}>/modules/system/admin.php?fct=version&mid=<{$module.dirname}>","Info",300,230);'><img src="<{$xoops_url}>/modules/system/images/info.png" alt="<{$lang_info}>" title="<{$lang_info}>" /></a>
      </td>
    </tr>
  <{/foreach}>
</table>