
/* Start Main Menu */
#mainmenu {font-size: .75em;}
#mainmenu a {display: block; margin: 0; padding: 2px; border-bottom: 1px dashed #bbb; color: #666;}
#mainmenu a:hover {color: #000; background: url(img/nav_bg.gif) no-repeat; background-position: left;}
#mainmenu a.menuTop {padding-left: 15px;}
#mainmenu a.menuMain {padding-left: 15px;}
#mainmenu a.menuSub {padding-left: 27px;}
#mainmenu a.menuSub:hover {}
/* End Main Menu */

/* Start User Menu */
#usermenu {font-size: .75em;}
#usermenu a {display: block; margin: 0; padding: 2px; border-bottom: 1px dashed #bbb; color: #666; padding-left: 15px;}
#usermenu a:hover {color: #000; background: url(img/nav_bg.gif) no-repeat; background-position: left;}
#usermenu a.menuTop {padding-left: 15px;}
#usermenu a.highlight {background: url(img/nav_bg.gif) no-repeat; background-position: left; color: #59BBE6;}
/* End User Menu */

/* tables and cells */
table {	width: 100%;}
table td {padding: 0; border-width: 0;	vertical-align: top;}
th {color: #0066CC; padding : 2px; vertical-align : middle;}
.outer {border: 1px solid #999; width: 100%;}
.head {background-color: #f2f2f2; color:inherit; padding: 5px; font-weight: bold;}
.even {background-color: #FFF; color:inherit; padding: 5px;}
.odd {background-color: #E9E9E9; color:inherit;padding: 5px; border-bottom: 1px solid #999;}
.foot {background-color: #DDD; color: inherit; padding: 1px;}
tr.even td {background-color: #e8e6e2; color:inherit; padding: 5px;}
tr.odd td {background-color: #E9E9E9; color:inherit; padding: 5px;}
th {border-bottom: 4px solid #F3AC03;}

/* core messages */
.errorMsg { background-color: #FFCCCC;  color:inherit; text-align: center; border-top: 1px solid #DDDDFF; border-left: 1px solid #DDDDFF; border-right: 1px solid #aaa; border-bottom: 1px solid #aaa;font-weight: bold; padding: 10px;}
.confirmMsg { background-color: #DDFFDF; color: #136C99; text-align: center; border-top: 1px solid #DDDDFF; border-left: 1px solid #DDDDFF; border-right: 1px solid #aaa; border-bottom: 1px solid #aaa;font-weight: bold; padding: 10px;}
.resultMsg { background-color : #bbb; color: #333; text-align: center; border-top: 1px solid #ccc; border-left: 1px solid #ccc; font-weight: bold; border-right: 1px solid #666; border-bottom: 1px solid #666; padding: 10px;}

/* codes and quotes */
.xoopsCode { background-color: #fff; color:inherit; border: 1px inset #000080; font-family: "Courier New",Courier,monospace; padding: 0 6px 6px 6px; max-height: 200px; overflow: auto;}
.xoopsQuote { background-color: #fff; color:inherit; border: 1px inset #000080; font-family: "Courier New",Courier,monospace; font-style:italic; padding: 0 6px 6px 6px;}
.icmsCode { background-color: #fff; color:inherit; border: 1px inset #000080; font-family: "Courier New",Courier,monospace; padding: 0 6px 6px 6px; max-height: 200px; overflow: auto;}
.icmsQuote { background-color: #fff; color:inherit; border: 1px inset #000080; font-family: "Courier New",Courier,monospace; font-style:italic; padding: 0 6px 6px 6px;}

/* Admin Help Nav */
#helpbar a {float: left; width: 14px; height: 14px; padding: 2px 0 4px 5px; background: #F3AC03; color: #333333; border: 1px dotted #dddddd;}

/* articles */
.item {border: 1px solid #ccc;}
.itemHead {padding-left: 3px; color: #639ACE; border-bottom: 1px solid #bbb; font-size: .8em;}
.itemInfo {text-align: right; padding: 3px; background-color: #efefef; color:inherit;}
.itemTitle a {font-size: 1.1em; font-weight: bold; font-variant: small-caps; color: #fff; background-color: transparent;}
.itemPoster {font-size: .9em; font-style:italic;}
.itemPostDate {font-size: .9em; font-style:italic;}
.itemStats {font-size: .9em; font-style:italic;}
.itemBody {padding-left: 5px;}
.itemText {margin-top: 5px; margin-bottom: 5px; line-height: 1.5em;}
.itemText:first-letter {font-size: 1.3em; font-weight: bold;}
.itemFoot {text-align: right; padding: 3px; background-color: #efefef; color:inherit;}
.itemAdminLink {font-size: .9em;}
.itemPermaLink {font-size: .9em;}

/* forums */
.row1 {background-color: #FFF; color:inherit; padding: 5px;}
.row2 {background-color: #E9E9E9; color:inherit;padding: 5px;}
.comTitle { font-weight: bold; margin-bottom: 2px;}
.comText {padding: 2px;}
.comUserStat {font-size: .7em; color: #639ACE; font-weight:bold; border: 1px solid #ccc; background-color: #fff; margin: 2px; padding: 2px; border-right: 2px solid #999; border-bottom: 2px solid #999;}
.comUserStatCaption {font-weight: normal;}
.comUserRank {margin: 2px;}
.comUserRankText {font-size: .8em;}
.comUserRankImg {border: 0; vertical-align: middle;}
.comUserStatus {float: left;}
.comUserName {font-size: 18px;}
.comUserName img {padding: 0;}

.comUserImg {margin: 2px;}
.comDate {font-weight: normal; font-style: italic; font-size: .8em}
.comDateCaption {font-weight: bold; font-style: normal; font-size: .8em}
.signature { font-size:.8em; font-style:italic;}
#online {color:green; font-weight:700; font-size:1em;}
#offline {color:red; font-weight:700; font-size:1em;}

.forum_controls {background-color: #EEE; border-left: 1px solid #999; border-right: 1px solid #999; border-top: 1px solid #999;width: 100%;}
.userrow {padding: 0;}
.Userrow_left {}
.Userrow_center {}
.Userrow_right {}
.transfer a {padding-left: 5px;}

#groupform .icms_checkboxoption {width: 48.9%;padding: 2px;}
.icms_checkboxoption {float: left;}
.icms_checkboxoption:hover {background: #f1f2f3;}
.icms_checkboxoption input[type=checkbox] {float: left; outline: none; background: transparent; border: 0;}
.icms_checkboxoption label {float: left; overflow: hidden; vertical-align: middle;}
.icms_checkboxoption label a {float: left; overflow: hidden;}
#groupform .even b {clear: both; margin: 5px 0px; float: left; width: 99%; background: #dcdee0; padding: 3px; border-bottom: 1px solid #ccc;}