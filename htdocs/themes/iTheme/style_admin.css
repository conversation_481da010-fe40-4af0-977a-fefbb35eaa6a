@import url(layout.css);
@import url(blocks.css);
@import url(content.css);


/* ------ General styles ------ */

html, body {
	color: #000;
	background: #FFF;
	margin: 0;
	padding: 0;
	font-family: Verdana, Arial, Helvetica, sans-serif;
	font-size: 10pt;
}

#xo-canvas {
	float: right;
}

table {
	width: 100%;
}

table td {
	padding: 0;
	border-width: 0;
	vertical-align: top;
}

img {
	border: 0;
}

a {
	color: #666;
	text-decoration: none;
	font-weight: bold;
}

a:hover {
	color: #8e8e8e;
}

h1 {font-size: 1.4em; }
h2 {font-size: 1.2em;}
h3 {font-size: 1em;}
h4 {font-size: 1em;}
h5 {font-size: .9em;}
h6 {font-size: .8em;}

/* ------ Header rules ------ */
#xo-header {
	clear: both;
	height: 78px;
	position: relative;
	background: url(img/header_bg.jpg) repeat-x;
	margin-left: 35px;
	margin-right: 35px;
}
#xo-headerlogo {
	float: left;
}
#xo-headerbanner {
	position:relative;
	top:10px;
	right:20px;
	float: right;
}
/* ------ Header top menu navigation rules ------ */
#xo-globalnav {
	clear: both;
	padding-left: 11px;
	margin-top: 40px;
	padding-bottom: 7px;
}
#xo-globalnav a {
	text-decoration: none;
	text-align: center;
	display: inline;
	padding-right: 10px;
	padding-left: 10px;
	background-color: #8bbcdd;
	padding-bottom: 7px;
	padding-top: 2px;
}
#xo-globalnav a:hover {
	color: #fff;
	border-top: 4px solid #F3AC03;
}
#controlbar {
	color: #F3AC03;
	font-weight: bold;
	padding-bottom: 3px;
	border-bottom: 1px dashed #8DBDE1;

}

/* ---------- Column left/right rules ---------- */

#xo-canvas-rightcolumn {}
#xo-canvas-rightcolumn a {}
#xo-canvas-rightcolumn a:hover {}

#xo-canvas-leftcolumn {}
#xo-canvas-leftcolumn a {}
#xo-canvas-leftcolumn a:hover {}

/* ------ Main content ------ */
#xo-canvas-content {
	line-height: 1.3em;
	z-index: 0;
	padding-top: 8px;
}

/* lists */
#xo-canvas-content ul {margin: 5px; padding: 2px; list-style: decimal inside; text-align: left;}
#xo-canvas-content li {margin-left: 5px; color: #000; background-color: inherit; line-height: 1.4em; list-style: circle;}

#xo-page {}

/* ---------- Module display rules ---------- */

#xo-content {
	float: left;
	padding: 15px 1%;
	width: 100%;
}
.CPbigTitle{
	background: no-repeat left top;
	border-bottom: 3px solid #1E90FF;
	color: #1E90FF;
	font-size: 20px;
	font-weight: bold;
	height: 40px;
	padding: 10px 0 0 50px;
	vertical-align: middle;
}
.CPindexOptions{
	padding: 4px;
	vertical-align: top;
}
.CPmediumTitle{
	color: #FF4500;
	font-weight: bold;
	font-size: 14px;
}
div.cpicon{
	font-family: Arial, Helvetica, sans-serif;
	margin: 3px;
	text-align: center;
}
div.cpicon a {
	border: 1px solid #CCCCCC;
	color: #666666;
	display: inline-block;
	height: 70px !important;
	margin: 0 0 4px;
	padding: 2px 5px 1px 5px;
	text-decoration : none;
	vertical-align: middle;
	width: 70px !important;
}
div.cpicon a:hover{
	background-color: #FFF6C1;
	border: 1px solid #FF9900;
	color: #1E90FF;
}
div.cpicon img {
	height: 32px;
	margin-top: 1px;
	margin-bottom: 1px;
	width: 32px;
}
div.cpicon span {
	display: block;
	font-size: 11px;
	font-weight: bold;
	overflow: hidden;
}
div.cpicon span.uno{
	color: blue;
	font-size: 11px;
	font-weight: normal;
	text-decoration: underline;
}
div.cpicon span.unor{
	color: #CC0000;
	font-size: 11px;
	font-weight: normal;
	text-decoration: underline;
}
/* ---------- Redirect ------------ */
#xo-redirect {
	background-color: #F2F2F2;
	border: 2px solid #DDDDFF;
	color: #222;
	font-weight: bold;
	margin: 40px auto;
	padding: 10px;
	text-align: center;
	width: 85%;
}
/* ---------- Site close ------------ */
#xo-siteclose-login {
	width: 270px;
	margin-left: auto;
	margin-right: auto;
	border:1px solid #DDDDFF;
	margin-top: 40px;
	margin-bottom: 20px;
}
#xo-siteclose-login h2 {
	background-color: #f2f2f2;
	color: white;
	font-weight: bold;
	font-size: 1em;
	margin-top: 0px;
	margin-bottom: 0px;
	padding: 2px;
	text-align: center;
}
#xo-siteclose-login form {
	margin: 0px;
	padding: 0px;
}
#xo-siteclose-login div{
	padding: 6px;
}
#xo-siteclose-login span.left {
	float: left;
	padding-bottom: 3px;
}
#xo-siteclose-login span.right {
	float: right;
	padding-right: 3px;
	padding-bottom: 3px;
}
#xo-siteclose-login div.button {
	clear: both;
	padding-right: 0px;
	text-align: right;
}
/* ---------- Footer rules ---------- */
#xo-footer {
	background: #59b9e4 url(img/foot_bg.jpg) repeat-x;
	color: #666;
	font-size: 70%;
	margin-top: 5px;
	padding-top: 10px;
	text-align: center;
}

#xo-footer a {
	color: #333;
}

#xo-footer a:hover {
	color: #fff;
}

/* ----------  mode debug  ---------- */

#xo-logger-output {
	font-size:	80%;
}

/* ---------- Image Manager / Folder not writeable ------------ */
tr.blocked td {background-color: #FF0000;}