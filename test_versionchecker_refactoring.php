<?php
/**
 * Test script to verify the version checker refactoring
 */

// Define the root path (normally done by ImpressCMS)
define('ICMS_ROOT_PATH', __DIR__ . '/htdocs');

// Include the necessary files
require_once ICMS_ROOT_PATH . '/libraries/icms/core/VersioncheckerInterface.php';
require_once ICMS_ROOT_PATH . '/libraries/icms/core/Versionchecker.php';
require_once ICMS_ROOT_PATH . '/libraries/icms/core/Versionchecker_RSS.php';

// Mock the constants that would normally be defined by ImpressCMS
if (!defined('ICMS_VERSION_NAME')) {
    define('ICMS_VERSION_NAME', 'ImpressCMS 2.0.1');
}
if (!defined('ICMS_VERSION_BUILD')) {
    define('ICMS_VERSION_BUILD', 113);
}
if (!defined('_AM_VERSION_CHECK_RSSDATA_EMPTY')) {
    define('_AM_VERSION_CHECK_RSSDATA_EMPTY', 'No RSS data available');
}

echo "Testing Version Checker Refactoring...\n\n";

// Test 1: Check if interface exists
echo "1. Testing interface existence: ";
if (interface_exists('icms_core_VersioncheckerInterface')) {
    echo "✓ PASS\n";
} else {
    echo "✗ FAIL\n";
}

// Test 2: Check if abstract base class exists
echo "2. Testing abstract base class: ";
if (class_exists('icms_core_Versionchecker')) {
    $reflection = new ReflectionClass('icms_core_Versionchecker');
    if ($reflection->isAbstract()) {
        echo "✓ PASS\n";
    } else {
        echo "✗ FAIL (not abstract)\n";
    }
} else {
    echo "✗ FAIL (class not found)\n";
}

// Test 3: Check if RSS implementation exists
echo "3. Testing RSS implementation: ";
if (class_exists('icms_core_Versionchecker_RSS')) {
    echo "✓ PASS\n";
} else {
    echo "✗ FAIL\n";
}

// Test 4: Check if RSS implementation implements interface
echo "4. Testing interface implementation: ";
if (class_exists('icms_core_Versionchecker_RSS')) {
    $reflection = new ReflectionClass('icms_core_Versionchecker_RSS');
    if ($reflection->implementsInterface('icms_core_VersioncheckerInterface')) {
        echo "✓ PASS\n";
    } else {
        echo "✗ FAIL\n";
    }
} else {
    echo "✗ FAIL (class not found)\n";
}

// Test 5: Check if RSS implementation extends base class
echo "5. Testing inheritance: ";
if (class_exists('icms_core_Versionchecker_RSS')) {
    if (is_subclass_of('icms_core_Versionchecker_RSS', 'icms_core_Versionchecker')) {
        echo "✓ PASS\n";
    } else {
        echo "✗ FAIL\n";
    }
} else {
    echo "✗ FAIL (class not found)\n";
}

// Test 6: Check if backward compatibility alias exists
echo "6. Testing backward compatibility: ";
if (class_exists('icms_core_Versionchecker_Original')) {
    echo "✓ PASS\n";
} else {
    echo "✗ FAIL\n";
}

echo "\nRefactoring test completed!\n";
